import { GalaxyRefProjectQueryDto } from '@n8n/api-types';
import { Logger } from '@n8n/backend-common';
import { Get, Query, RestController } from '@n8n/decorators';

import { GalaxyService } from '../galaxy/galaxy-permission.service';

@RestController('/galaxy')
export class WorkflowsController {
	constructor(
		private readonly logger: Logger,
		private readonly galaxyService: GalaxyService,
	) {}

	@Get('/refProject')
	async getAllRef(@Query query: GalaxyRefProjectQueryDto) {
		const { uid } = query;

		try {
			const projects = await this.galaxyService.getRefProject(uid);
			return {
				success: true,
				data: projects,
			};
		} catch (error) {
			this.logger.error('Failed to get reference projects', {
				uid,
				error: error instanceof Error ? error.message : 'Unknown error',
			});
			throw error;
		}
	}
}
