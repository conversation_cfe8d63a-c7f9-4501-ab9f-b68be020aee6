import type { IRestApiContext } from '../types';
import { makeRestApiRequest } from '../utils';

export interface GalaxyProject {
	projectId: string;
	projectName: string;
}

export interface GalaxyRefProjectResponse {
	code: number;
	message: string;
	data: GalaxyProject[];
}

export async function getGalaxyRefProjects(
	context: IRestApiContext,
	uid: string,
): Promise<GalaxyRefProjectResponse> {
	return await makeRestApiRequest(
		context,
		'GET',
		`/galaxy/refProject?uid=${encodeURIComponent(uid)}`,
	);
}
