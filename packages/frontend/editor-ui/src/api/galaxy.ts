import type { IRestApiContext } from '@n8n/rest-api-client';
import {
	getGalaxyRefProjects,
	type GalaxyProject,
	type GalaxyRefProjectResponse,
} from '@n8n/rest-api-client';

export async function getGalaxyProjectOptions(
	context: IRestApiContext,
	uid: string,
): Promise<GalaxyProject[]> {
	const response: GalaxyRefProjectResponse = await getGalaxyRefProjects(context, uid);

	if (response.code === 0) {
		return response.data;
	} else {
		throw new Error(response.message || 'Failed to fetch Galaxy projects');
	}
}
